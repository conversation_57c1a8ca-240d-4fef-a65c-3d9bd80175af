"""
AI Template Module for GretahAI ScriptWeaver Stage 10.

This module provides specialized AI functionality for template-based script generation
in the Script Playground (Stage 10). It follows the established modular AI architecture
used throughout the application.

Functions:
    generate_template_based_script: Main function for template-based script generation
    extract_python_code_from_response: Helper function to clean AI responses
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

# Import the centralized AI function
from .ai import generate_llm_response
from .template_prompt_builder import (
    generate_template_based_script_prompt,
    enhance_template_prompt_with_context
)
from .template_helpers import extract_template_structure_info

# Set up logging
logger = logging.getLogger(__name__)


def generate_template_based_script(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    custom_instructions: str = None,
    preserve_structure: bool = True,
    include_error_handling: bool = True,
    website_url: str = None,
    api_key: str = None,
    model_name: str = "gemini-2.0-flash"
) -> Optional[str]:
    """
    Generate a new test script using an optimized script as a template.
    
    This function orchestrates the template-based script generation process by:
    1. Extracting template structure information
    2. Building the AI prompt with template context
    3. Enhancing the prompt with additional context
    4. Calling the centralized AI function
    5. Cleaning and validating the response
    
    Args:
        template_script: The optimized script to use as template
        target_test_case: The target test case for generation
        custom_instructions: User's custom instructions (optional)
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
        website_url: Target website URL (optional)
        api_key: Google AI API key (optional)
        model_name: AI model to use
        
    Returns:
        str: The generated and cleaned Python script, or None if generation failed
    """
    try:
        logger.info("Starting template-based script generation")
        
        # Extract template structure information
        template_structure_info = extract_template_structure_info(
            template_script.get('content', '')
        )
        
        # Generate the base prompt
        base_prompt = generate_template_based_script_prompt(
            template_script=template_script,
            target_test_case=target_test_case,
            template_structure_info=template_structure_info,
            website_url=website_url
        )
        
        # Enhance prompt with additional context
        additional_context = {
            'custom_instructions': custom_instructions if custom_instructions else None,
            'preserve_structure': preserve_structure,
            'include_error_handling': include_error_handling
        }
        
        enhanced_prompt = enhance_template_prompt_with_context(base_prompt, additional_context)
        
        # Generate script using the centralized AI function
        logger.info("Calling centralized AI function for template-based script generation")
        generated_script = generate_llm_response(
            prompt=enhanced_prompt,
            model_name=model_name,
            api_key=api_key,
            category="template_script_generation",
            function_name="generate_template_based_script",
            context={
                'template_test_case_id': template_script.get('test_case_id', 'unknown'),
                'target_test_case_id': target_test_case.get('Test Case ID', 'unknown'),
                'template_script_id': template_script.get('id', 'unknown'),
                'generation_type': 'template_based',
                'preserve_structure': preserve_structure,
                'include_error_handling': include_error_handling,
                'has_custom_instructions': bool(custom_instructions)
            }
        )
        
        if generated_script and generated_script.strip():
            # Clean up the response to extract Python code
            cleaned_script = extract_python_code_from_response(generated_script)
            
            if cleaned_script and cleaned_script.strip():
                logger.info("Template-based script generation completed successfully")
                return cleaned_script
            else:
                logger.error("Failed to extract valid Python code from generated response")
                return None
        else:
            logger.error("Template-based script generation failed - empty response")
            return None
            
    except Exception as e:
        logger.error(f"Template-based script generation failed: {e}")
        return None


def extract_python_code_from_response(response_text: str) -> str:
    """
    Extract Python code from AI response, removing markdown formatting.
    
    This function handles various markdown formats that AI models might use
    when returning code, including triple backticks with language identifiers.
    
    Args:
        response_text: Raw response from AI that may contain markdown code blocks
        
    Returns:
        str: Cleaned Python code without markdown formatting
    """
    try:
        logger.debug("Extracting Python code from AI response")
        
        if not response_text or not isinstance(response_text, str):
            logger.debug("Invalid response text provided")
            return ""
        
        cleaned_response = response_text.strip()
        
        # Check for markdown code blocks with triple backticks
        if "```" in cleaned_response:
            logger.debug("Found markdown code blocks in response")
            
            # Use simple string splitting approach (most reliable)
            parts = cleaned_response.split("```")
            if len(parts) >= 3:
                logger.debug(f"Found {len(parts)} parts after splitting by backticks")
                # Take the middle part (between first and last ```)
                middle_part = parts[1]
                logger.debug(f"Middle part length: {len(middle_part)} characters")
                
                # Remove language identifier if it's on the first line
                lines = middle_part.split('\n')
                if lines and lines[0].strip() in ['python', 'py', 'Python']:
                    logger.debug("Removing Python language identifier from first line")
                    cleaned_response = '\n'.join(lines[1:]).strip()
                else:
                    cleaned_response = middle_part.strip()
                
                logger.debug(f"Extracted code block: {len(cleaned_response)} characters")
            else:
                logger.debug("Could not find proper code block structure")
                return ""
        else:
            logger.debug("No markdown code blocks found, using response as-is")
        
        # Additional cleanup: remove any remaining markdown artifacts
        lines = cleaned_response.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Skip lines that are clearly markdown artifacts
            if line.strip().startswith('```'):
                continue
            cleaned_lines.append(line)
        
        final_code = '\n'.join(cleaned_lines).strip()
        logger.debug(f"Final cleaned code: {len(final_code)} characters")
        
        # Basic validation: check if it looks like Python code
        if final_code and ('import ' in final_code or 'def ' in final_code or 'pytest' in final_code):
            logger.debug("Code validation passed - looks like valid Python")
            return final_code
        else:
            logger.debug("Code validation failed - doesn't look like valid Python")
            logger.debug(f"Code preview: {final_code[:200]}...")
            return ""
            
    except Exception as e:
        logger.error(f"Error extracting Python code from response: {e}")
        return ""
